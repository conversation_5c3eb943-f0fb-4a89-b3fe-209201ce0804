
-- Connection Logic

CreateThread(function()

    while not ESX.PlayerLoaded do
        Wait(100)

        if NetworkIsPlayerActive(ESX.playerId) then
            ESX.DisableSpawnManager()
            DoScreenFadeOut(0)

            -- إزالة الاعتماد على esx_context
            Multicharacter:SetupCharacters()
            break
        end
    end
end)

-- Events

ESX.SecureNetEvent("esx_multicharacter:SetupUI", function(data, slots)
    Multicharacter:SetupUI(data, slots)
end)

RegisterNetEvent('esx:playerLoaded', function(playerData, isNew, skin)
    Multicharacter:PlayerLoaded(playerData, isNew, skin)
end)

-- معالج أحداث NUI
RegisterNUICallback('menuAction', function(data, cb)
    if Menu.onUse then
        Menu.onUse(nil, data)
    end
    cb('ok')
end)

RegisterNUICallback('closeMenu', function(data, cb)
    Menu:Close()
    cb('ok')
end)

-- أمر لاختيار الشخصية (نظام بديل)
RegisterCommand('selectchar', function(source, args)
    if not ESX.PlayerLoaded then
        local charIndex = tonumber(args[1])
        if charIndex and Menu.currentElements[charIndex] and not Menu.currentElements[charIndex].unselectable then
            if Menu.onUse then
                Menu.onUse(nil, Menu.currentElements[charIndex])
            end
        else
            TriggerEvent('chat:addMessage', {
                color = { 255, 0, 0 },
                args = { "Error", "Invalid character selection" }
            })
        end
    end
end, false)

-- أمر لإنشاء شخصية جديدة
RegisterCommand('newchar', function(source, args)
    if not ESX.PlayerLoaded then
        Menu:NewCharacter()
    end
end, false)

ESX.SecureNetEvent('esx:onPlayerLogout', function()
    DoScreenFadeOut(500)
    Wait(5000)

    Multicharacter.spawned = false

    Multicharacter:SetupCharacters()
    if GetResourceState("esx_skin") == "started" then
        TriggerEvent("esx_skin:resetFirstSpawn")
    end
end)

-- Relog

if Config.Relog then
    RegisterCommand("relog", function()
        if Multicharacter.canRelog then
            Multicharacter.canRelog = false
            TriggerServerEvent("esx_multicharacter:relog")

            ESX.SetTimeout(10000, function()
                Multicharacter.canRelog = true
            end)

        end
    end,false)
end
