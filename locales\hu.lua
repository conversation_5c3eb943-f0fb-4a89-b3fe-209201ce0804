Locales["hu"] = {
    -- Inventory
    ["inventory"] = "Inventory ( Súly %s / %s )",
    ["use"] = "Hasz<PERSON>l",
    ["give"] = "Átad",
    ["remove"] = "Eldob",
    ["return"] = "Visszatérés",
    ["give_to"] = "Adni valakinek",
    ["amount"] = "Mennyiség",
    ["giveammo"] = "Lőszer adás",
    ["amountammo"] = "Lőszer mennyiség",
    ["noammo"] = "Nincsen több lövedéked!",
    ["gave_item"] = "Átadtál: %sx %s neki: %s",
    ["received_item"] = "Kaptál: %sx %s töle: %s",
    ["gave_weapon"] = "Átadtál: %s neki: %s",
    ["gave_weapon_ammo"] = "Átadtál ~o~%sx %s %s neki: %s",
    ["gave_weapon_withammo"] = "Átadtál %s ~o~%sx %s neki: %s",
    ["gave_weapon_hasalready"] = "%s már rendelkezik %s",
    ["gave_weapon_noweapon"] = "%s nincsen ilyen fegyere",
    ["received_weapon"] = "Kaptál: %s töle: %s",
    ["received_weapon_ammo"] = "Kaptál ~o~%sx %s %s töle: %s",
    ["received_weapon_withammo"] = "Kaptál %s ~o~%sx %s töle: %s",
    ["received_weapon_hasalready"] = "%s megpróbálta átadni a következöt: %s, nem már van rendelkezel egy ilyennel",
    ["received_weapon_noweapon"] = "%s átakart adni %s, de nincsen ilyen fegyvered",
    ["gave_account_money"] = "Átadtál: $%s (%s) neki: %s",
    ["received_account_money"] = "Kaptál: $%s (%s) töle: %s",
    ["amount_invalid"] = "Érvénytelen mennyiség",
    ["players_nearby"] = "Nincsen játékos a közeledben",
    ["ex_inv_lim"] = "Nincsen elég szabad helyed %s",
    ["imp_invalid_quantity"] = "Érvénytelen mennyiség",
    ["imp_invalid_amount"] = "Érvénytelen összeg",
    ["threw_standard"] = "Kidobtál: %sx %s",
    ["threw_account"] = "Kidobtál: $%s %s",
    ["threw_weapon"] = "Kidobtál: %s",
    ["threw_weapon_ammo"] = "Kidobtál: %s ~o~%sx %s",
    ["threw_weapon_already"] = "Van már ilyen fegyvered",
    ["threw_cannot_pickup"] = "Nincsen elég szabad helyed",
    ["threw_pickup_prompt"] = "E hogy felvedd",

    -- Key mapping
    ["keymap_showinventory"] = "Leltár mutatása",

    -- Salary related
    ["received_salary"] = "Megkaptad a fizetésed: $%s",
    ["received_help"] = "Megkaptad a segélyt: $%s",
    ["company_nomoney"] = "Nem kaptál fizetést, mert a frakciónak nincs elég pénze!",
    ["received_paycheck"] = "Fizetésed érkezett!",
    ["bank"] = "maze Bank",
    ["account_bank"] = "bank",
    ["account_black_money"] = "Piszkos pénz",
    ["account_money"] = "Készpénz",

    ["act_imp"] = "Érvénytelen mennyiség",
    ["in_vehicle"] = "Nem tudod átadni, mivel benne ül a jármüben",
    ["not_in_vehicle"] = "Cannot Perform Action, Player isn't in a vehicle",

    -- Commands
    ["command_bring"] = "Játékos magadhoz teleportálása",
    ["command_car"] = "Jármű lehívása",
    ["command_car_car"] = "Jármű név vagy hash",
    ["command_cardel"] = "Közeli járművek törlése",
    ["command_cardel_radius"] = "Megadott radiusban lévő járművek törlése",
    ["command_repair"] = "Repair your vehicle",
    ["command_repair_success"] = "Successfully repaired vehicle",
    ["command_repair_success_target"] = "An admin repaired your vehicle",
    ["command_clear"] = "Chat ürítése",
    ["command_clearall"] = "Chat ürítése minden játékosnál",
    ["command_clearinventory"] = "Minden tárgy törlése a játékos inventoryból",
    ["command_clearloadout"] = "Minden fegyver elvétele a játékostól",
    ["command_freeze"] = "Játékos fagyasztása",
    ["command_unfreeze"] = "Játékos kiolvasztása",
    ["command_giveaccountmoney"] = "Pénz adás a megadott típusban",
    ["command_giveaccountmoney_account"] = "Típus",
    ["command_giveaccountmoney_amount"] = "Mennyiség",
    ["command_giveaccountmoney_invalid"] = "Megadott típus hibás",
    ["command_giveitem"] = "Tárgy adás a játékosnak",
    ["command_giveitem_item"] = "Tárgy neve",
    ["command_giveitem_count"] = "Mennyiség",
    ["command_giveweapon"] = "Fegyver adás játékosnak",
    ["command_giveweapon_weapon"] = "Fegyver neve",
    ["command_giveweapon_ammo"] = "Lőszer mennyiség",
    ["command_giveweapon_hasalready"] = "Játékosnak már van ilyen fegyvere",
    ["command_giveweaponcomponent"] = "Fegyver kiegészítő adás a játékosnak",
    ["command_giveweaponcomponent_component"] = "Kiegészítő neve",
    ["command_giveweaponcomponent_invalid"] = "Érvénytelen fegyver kiegészítő",
    ["command_giveweaponcomponent_hasalready"] = "Játékosnak már van ilyen fegyver kiegészítője",
    ["command_giveweaponcomponent_missingweapon"] = "Játékosnak nincs ilyen fegyvere",
    ["command_goto"] = "Teleportálás játékoshoz",
    ["command_kill"] = "Játékos megölése",
    ["command_save"] = "Játékos adatainak mentése",
    ["command_saveall"] = "Összes játékos adatainak mentése",
    ["command_setaccountmoney"] = "Játékos pénzének beállítása megadott típusban",
    ["command_setaccountmoney_amount"] = "Mennyiség",
    ["command_setcoords"] = "Teleportálás megadott kordinátára",
    ["command_setcoords_x"] = "X érték",
    ["command_setcoords_y"] = "Y érték",
    ["command_setcoords_z"] = "Z érték",
    ["command_setjob"] = "Játékos munkájának beállítása",
    ["command_setjob_job"] = "Munka neve",
    ["command_setjob_grade"] = "Rang",
    ["command_setjob_invalid"] = "Munka név vagy rang érvénytelen",
    ["command_setgroup"] = "Játékos jogosultsági csoportjának beállítása",
    ["command_setgroup_group"] = "Csoport neve",
    ["commanderror_argumentmismatch"] = "Érvénytelen argumentumszám (%s megadva, %s szükséges)",
    ["commanderror_argumentmismatch_number"] = "Érvénytelen argumentum #%s adat típus (szöveg megadva, szám szükséges)",
    ["commanderror_argumentmismatch_string"] = "Invalid Argument #%s data type (passed number, wanted string)",
    ["commanderror_invaliditem"] = "Érvénytelen tárgy",
    ["commanderror_invalidweapon"] = "Érvénytelen fegyver",
    ["commanderror_console"] = "Parancs nem használható konzolból",
    ["commanderror_invalidcommand"] = "Érvénytelen parancs - /%s",
    ["commanderror_invalidplayerid"] = "Megadott játékos nem online.",
    ["commandgeneric_playerid"] = "Játékos Szerver Id",
    ["command_giveammo_noweapon_found"] = "Nincs ilyen fegyvered: %s",
    ["command_giveammo_weapon"] = "Fegyver név",
    ["command_giveammo_ammo"] = "Lőszer mennyiség",
    ["tpm_nowaypoint"] = "Nincs kijelölve pozíció!",
    ["tpm_success"] = "Sikeres teleportálás",

    ["noclip_message"] = "Noclip %s",
    ["enabled"] = "~g~engedélyezve~s~",
    ["disabled"] = "~r~letiltva~s~",

    -- Locale settings
    ["locale_digit_grouping_symbol"] = ",",
    ["locale_currency"] = "$%s",

    -- Weapons

    -- Melee
    ["weapon_dagger"] = "Dagger",
    ["weapon_bat"] = "Bat",
    ["weapon_battleaxe"] = "Battle Axe",
    ["weapon_bottle"] = "Bottle",
    ["weapon_crowbar"] = "Crowbar",
    ["weapon_flashlight"] = "Flashlight",
    ["weapon_golfclub"] = "Golf Club",
    ["weapon_hammer"] = "Hammer",
    ["weapon_hatchet"] = "Hatchet",
    ["weapon_knife"] = "Knife",
    ["weapon_knuckle"] = "Knuckledusters",
    ["weapon_machete"] = "Machete",
    ["weapon_nightstick"] = "Nightstick",
    ["weapon_wrench"] = "Pipe Wrench",
    ["weapon_poolcue"] = "Pool Cue",
    ["weapon_stone_hatchet"] = "Stone Hatchet",
    ["weapon_switchblade"] = "Switchblade",

    -- Handguns
    ["weapon_appistol"] = "AP Pistol",
    ["weapon_ceramicpistol"] = "Ceramic Pistol",
    ["weapon_combatpistol"] = "Combat Pistol",
    ["weapon_doubleaction"] = "Double-Action Revolver",
    ["weapon_navyrevolver"] = "Navy Revolver",
    ["weapon_flaregun"] = "Flaregun",
    ["weapon_gadgetpistol"] = "Gadget Pistol",
    ["weapon_heavypistol"] = "Heavy Pistol",
    ["weapon_revolver"] = "Heavy Revolver",
    ["weapon_revolver_mk2"] = "Heavy Revolver MK2",
    ["weapon_marksmanpistol"] = "Marksman Pistol",
    ["weapon_pistol"] = "Pistol",
    ["weapon_pistol_mk2"] = "Pistol MK2",
    ["weapon_pistol50"] = "Pistol .50",
    ["weapon_snspistol"] = "SNS Pistol",
    ["weapon_snspistol_mk2"] = "SNS Pistol MK2",
    ["weapon_stungun"] = "Taser",
    ["weapon_raypistol"] = "Up-N-Atomizer",
    ["weapon_vintagepistol"] = "Vintage Pistol",

    -- Shotguns
    ["weapon_assaultshotgun"] = "Assault Shotgun",
    ["weapon_autoshotgun"] = "Auto Shotgun",
    ["weapon_bullpupshotgun"] = "Bullpup Shotgun",
    ["weapon_combatshotgun"] = "Combat Shotgun",
    ["weapon_dbshotgun"] = "Double Barrel Shotgun",
    ["weapon_heavyshotgun"] = "Heavy Shotgun",
    ["weapon_musket"] = "Musket",
    ["weapon_pumpshotgun"] = "Pump Shotgun",
    ["weapon_pumpshotgun_mk2"] = "Pump Shotgun MK2",
    ["weapon_sawnoffshotgun"] = "Sawed Off Shotgun",

    -- SMG & LMG
    ["weapon_assaultsmg"] = "Assault SMG",
    ["weapon_combatmg"] = "Combat MG",
    ["weapon_combatmg_mk2"] = "Combat MG MK2",
    ["weapon_combatpdw"] = "Combat PDW",
    ["weapon_gusenberg"] = "Gusenberg Sweeper",
    ["weapon_machinepistol"] = "Machine Pistol",
    ["weapon_mg"] = "MG",
    ["weapon_microsmg"] = "Micro SMG",
    ["weapon_minismg"] = "Mini SMG",
    ["weapon_smg"] = "SMG",
    ["weapon_smg_mk2"] = "SMG MK2",
    ["weapon_raycarbine"] = "Unholy Hellbringer",

    -- Rifles
    ["weapon_advancedrifle"] = "Advanced Rifle",
    ["weapon_assaultrifle"] = "Assault Rifle",
    ["weapon_assaultrifle_mk2"] = "Assault Rifle MK2",
    ["weapon_bullpuprifle"] = "Bullpup Rifle",
    ["weapon_bullpuprifle_mk2"] = "Bullpup Rifle MK2",
    ["weapon_carbinerifle"] = "Carbine Rifle",
    ["weapon_carbinerifle_mk2"] = "Carbine Rifle MK2",
    ["weapon_compactrifle"] = "Compact Rifle",
    ["weapon_militaryrifle"] = "Military Rifle",
    ["weapon_specialcarbine"] = "Special Carbine",
    ["weapon_specialcarbine_mk2"] = "Special Carbine MK2",
    ["weapon_heavyrifle"] = "Heavy Rifle", -- Not Translated

    -- Sniper
    ["weapon_heavysniper"] = "Heavy Sniper",
    ["weapon_heavysniper_mk2"] = "Heavy Sniper MK2",
    ["weapon_marksmanrifle"] = "Marksman Rifle",
    ["weapon_marksmanrifle_mk2"] = "Marksman Rifle MK2",
    ["weapon_sniperrifle"] = "Sniper Rifle",

    -- Heavy / Launchers
    ["weapon_compactlauncher"] = "Compact Launcher",
    ["weapon_firework"] = "Firework Launcher",
    ["weapon_grenadelauncher"] = "Grenade Launcher",
    ["weapon_hominglauncher"] = "Homing Launcher",
    ["weapon_minigun"] = "Minigun",
    ["weapon_railgun"] = "Railgun",
    ["weapon_rpg"] = "Rocket Launcher",
    ["weapon_rayminigun"] = "Widowmaker",

    -- Criminal Enterprises DLC
    ["weapon_metaldetector"] = "Fémkereső",
    ["weapon_precisionrifle"] = "Precision Rifle",
    ["weapon_tactilerifle"] = "Service Carbine",

    -- Drug Wars DLC
    ["weapon_candycane"] = "Candy Cane", -- not translated
    ["weapon_acidpackage"] = "Acid Package", -- not translated
    ["weapon_pistolxm3"] = "WM 29 Pistol", -- not translated
    ["weapon_railgunxm3"] = "Railgun", -- not translated

    -- Thrown
    ["weapon_ball"] = "Baseball",
    ["weapon_bzgas"] = "BZ Gas",
    ["weapon_flare"] = "Flare",
    ["weapon_grenade"] = "Grenade",
    ["weapon_petrolcan"] = "Jerrycan",
    ["weapon_hazardcan"] = "Hazardous Jerrycan",
    ["weapon_molotov"] = "Molotov Cocktail",
    ["weapon_proxmine"] = "Proximity Mine",
    ["weapon_pipebomb"] = "Pipe Bomb",
    ["weapon_snowball"] = "Snowball",
    ["weapon_stickybomb"] = "Sticky Bomb",
    ["weapon_smokegrenade"] = "Tear Gas",

    -- Special
    ["weapon_fireextinguisher"] = "Fire Extinguisher",
    ["weapon_digiscanner"] = "Digital Scanner",
    ["weapon_garbagebag"] = "Garbage Bag",
    ["weapon_handcuffs"] = "Handcuffs",
    ["gadget_nightvision"] = "Night Vision",
    ["gadget_parachute"] = "parachute",

    -- Weapon Components
    ["component_knuckle_base"] = "base Model",
    ["component_knuckle_pimp"] = "the Pimp",
    ["component_knuckle_ballas"] = "the Ballas",
    ["component_knuckle_dollar"] = "the Hustler",
    ["component_knuckle_diamond"] = "the Rock",
    ["component_knuckle_hate"] = "the Hater",
    ["component_knuckle_love"] = "the Lover",
    ["component_knuckle_player"] = "the Player",
    ["component_knuckle_king"] = "the King",
    ["component_knuckle_vagos"] = "the Vagos",

    ["component_luxary_finish"] = "luxary Weapon Finish",

    ["component_handle_default"] = "default Handle",
    ["component_handle_vip"] = "vIP Handle",
    ["component_handle_bodyguard"] = "bodyguard Handle",

    ["component_vip_finish"] = "vIP Finish",
    ["component_bodyguard_finish"] = "bodyguard Finish",

    ["component_camo_finish"] = "digital Camo",
    ["component_camo_finish2"] = "brushstroke Camo",
    ["component_camo_finish3"] = "woodland Camo",
    ["component_camo_finish4"] = "skull Camo",
    ["component_camo_finish5"] = "sessanta Nove Camo",
    ["component_camo_finish6"] = "perseus Camo",
    ["component_camo_finish7"] = "leopard Camo",
    ["component_camo_finish8"] = "zebra Camo",
    ["component_camo_finish9"] = "geometric Camo",
    ["component_camo_finish10"] = "boom Camo",
    ["component_camo_finish11"] = "patriotic Camo",

    ["component_camo_slide_finish"] = "digital Slide Camo",
    ["component_camo_slide_finish2"] = "brushstroke Slide Camo",
    ["component_camo_slide_finish3"] = "woodland Slide Camo",
    ["component_camo_slide_finish4"] = "skull Slide Camo",
    ["component_camo_slide_finish5"] = "sessanta Nove Slide Camo",
    ["component_camo_slide_finish6"] = "perseus Slide Camo",
    ["component_camo_slide_finish7"] = "leopard Slide Camo",
    ["component_camo_slide_finish8"] = "zebra Slide Camo",
    ["component_camo_slide_finish9"] = "geometric Slide Camo",
    ["component_camo_slide_finish10"] = "boom Slide Camo",
    ["component_camo_slide_finish11"] = "patriotic Slide Camo",

    ["component_clip_default"] = "default Magazine",
    ["component_clip_extended"] = "extended Magazine",
    ["component_clip_drum"] = "drum Magazine",
    ["component_clip_box"] = "box Magazine",

    ["component_scope_holo"] = "holographic Scope",
    ["component_scope_small"] = "small Scope",
    ["component_scope_medium"] = "medium Scope",
    ["component_scope_large"] = "large Scope",
    ["component_scope"] = "mounted Scope",
    ["component_scope_advanced"] = "advanced Scope",
    ["component_ironsights"] = "ironsights",

    ["component_suppressor"] = "suppressor",
    ["component_compensator"] = "compensator",

    ["component_muzzle_flat"] = "flat Muzzle Brake",
    ["component_muzzle_tactical"] = "tactical Muzzle Brake",
    ["component_muzzle_fat"] = "fat-End Muzzle Brake",
    ["component_muzzle_precision"] = "precision Muzzle Brake",
    ["component_muzzle_heavy"] = "heavy Duty Muzzle Brake",
    ["component_muzzle_slanted"] = "slanted Muzzle Brake",
    ["component_muzzle_split"] = "split-End Muzzle Brake",
    ["component_muzzle_squared"] = "squared Muzzle Brake",

    ["component_flashlight"] = "flashlight",
    ["component_grip"] = "grip",

    ["component_barrel_default"] = "default Barrel",
    ["component_barrel_heavy"] = "heavy Barrel",

    ["component_ammo_tracer"] = "tracer Ammo",
    ["component_ammo_incendiary"] = "incendiary Ammo",
    ["component_ammo_hollowpoint"] = "hollowpoint Ammo",
    ["component_ammo_fmj"] = "fMJ Ammo",
    ["component_ammo_armor"] = "armor Piercing Ammo",
    ["component_ammo_explosive"] = "armor Piercing Incendiary Ammo",

    ["component_shells_default"] = "default Shells",
    ["component_shells_incendiary"] = "dragons Breath Shells",
    ["component_shells_armor"] = "steel Buckshot Shells",
    ["component_shells_hollowpoint"] = "flechette Shells",
    ["component_shells_explosive"] = "explosive Slug Shells",

    -- Weapon Ammo
    ["ammo_rounds"] = "round(s)",
    ["ammo_shells"] = "shell(s)",
    ["ammo_charge"] = "charge",
    ["ammo_petrol"] = "gallons of fuel",
    ["ammo_firework"] = "firework(s)",
    ["ammo_rockets"] = "rocket(s)",
    ["ammo_grenadelauncher"] = "grenade(s)",
    ["ammo_grenade"] = "grenade(s)",
    ["ammo_stickybomb"] = "bomb(s)",
    ["ammo_pipebomb"] = "bomb(s)",
    ["ammo_smokebomb"] = "bomb(s)",
    ["ammo_molotov"] = "cocktail(s)",
    ["ammo_proxmine"] = "mine(s)",
    ["ammo_bzgas"] = "can(s)",
    ["ammo_ball"] = "ball(s)",
    ["ammo_snowball"] = "snowball(s)",
    ["ammo_flare"] = "flare(s)",
    ["ammo_flaregun"] = "flare(s)",

    -- Weapon Tints
    ["tint_default"] = "default skin",
    ["tint_green"] = "green skin",
    ["tint_gold"] = "gold skin",
    ["tint_pink"] = "pink skin",
    ["tint_army"] = "army skin",
    ["tint_lspd"] = "blue skin",
    ["tint_orange"] = "orange skin",
    ["tint_platinum"] = "platinum skin",
    -- MK2 Weapon Tints
    ["tint_classic_black"] = "klasszikus fekete",
    ["tint_classic_gray"] = "klasszikus szürke",
    ["tint_classic_two_tone"] = "klasszikus két szín",
    ["tint_classic_white"] = "klasszikus fehér",
    ["tint_classic_beige"] = "klasszikus bézs",
    ["tint_classic_green"] = "klasszikus zöld",
    ["tint_classic_blue"] = "klasszikus kék",
    ["tint_classic_earth"] = "klasszikus föld",
    ["tint_classic_brown_black"] = "klasszikus barna-fekete",
    ["tint_contrast_red"] = "kontraszt piros",
    ["tint_contrast_blue"] = "kontraszt kék",
    ["tint_contrast_yellow"] = "kontraszt sárga",
    ["tint_contrast_orange"] = "kontraszt narancs",
    ["tint_bold_pink"] = "merész rózsaszín",
    ["tint_bold_purple_yellow"] = "merész lila-sárga",
    ["tint_bold_orange"] = "merész narancs",
    ["tint_bold_green_purple"] = "merész zöld-lila",
    ["tint_bold_red_feat"] = "merész piros feat",
    ["tint_bold_green_feat"] = "merész zöld feat",
    ["tint_bold_cyan_feat"] = "merész cián feat",
    ["tint_bold_yellow_feat"] = "merész sárga feat",
    ["tint_bold_red_white"] = "merész piros fehér",
    ["tint_bold_blue_white"] = "merész kék fehér",
    ["tint_metallic_gold"] = "metál arany",
    ["tint_metallic_platinum"] = "metál platina",
    ["tint_metallic_gray_lilac"] = "metál szürke lila",
    ["tint_metallic_purple_lime"] = "metál lila lime",
    ["tint_metallic_red"] = "metál piros",
    ["tint_metallic_green"] = "metál zöld",
    ["tint_metallic_blue"] = "metál kék",
    ["tint_metallic_white_aqua"] = "metál fehér aqua",
    ["tint_metallic_red_yellow"] = "metál piros sárga",
}
