Locales["id"] = {
    -- Inventory
    ["inventory"] = "Inventaris ( Berat %s / %s )",
    ["use"] = "Gunakan",
    ["give"] = "<PERSON>ri",
    ["remove"] = "<PERSON><PERSON>",
    ["return"] = "<PERSON><PERSON><PERSON>",
    ["give_to"] = "Beri ke",
    ["amount"] = "Jumlah",
    ["giveammo"] = "Beri amunisi",
    ["amountammo"] = "Jumlah Amunisi",
    ["noammo"] = "Tidak cukup!",
    ["gave_item"] = "Memberi %sx %s ke %s",
    ["received_item"] = "Menerima %sx %s dari %s",
    ["gave_weapon"] = "Memberi %s ke %s",
    ["gave_weapon_ammo"] = "Memberi ~o~%sx %s untuk %s ke %s",
    ["gave_weapon_withammo"] = "Memberi %s dengan ~o~%sx %s ke %s",
    ["gave_weapon_hasalready"] = "%s sudah memiliki %s",
    ["gave_weapon_noweapon"] = "%s tidak memiliki senjata tersebut",
    ["received_weapon"] = "Menerima %s dari %s",
    ["received_weapon_ammo"] = "Menerima ~o~%sx %s untuk %s Anda dari %s",
    ["received_weapon_withammo"] = "Menerima %s dengan ~o~%sx %s dari %s",
    ["received_weapon_hasalready"] = "%s mencoba memberimu %s, tapi kamu sudah memiliki senjata ini",
    ["received_weapon_noweapon"] = "%s mencoba memberimu amunisi untuk %s, tapi kamu tidak memiliki senjata ini",
    ["gave_account_money"] = "Memberi $%s (%s) ke %s",
    ["received_account_money"] = "Menerima $%s (%s) dari %s",
    ["amount_invalid"] = "Jumlah salah",
    ["players_nearby"] = "Tidak ada Player di sekitar",
    ["ex_inv_lim"] = "Tidak dapat melakukan aksi, melebihi batas berat dari %s",
    ["imp_invalid_quantity"] = "Tidak dapat melakukan aksi, jumlah salah",
    ["imp_invalid_amount"] = "Tidak dapat melakukan aksi, jumlah salah",
    ["threw_standard"] = "Membuang %sx %s",
    ["threw_account"] = "Membuang $%s %s",
    ["threw_weapon"] = "Membuang %s",
    ["threw_weapon_ammo"] = "Membuang %s dengan ~o~%sx %s",
    ["threw_weapon_already"] = "Kamu sudah memiliki senjata ini",
    ["threw_cannot_pickup"] = "Inventaris penuh, Tidak dapat mengambil!",
    ["threw_pickup_prompt"] = "Tekan E untuk Mengambil",

    -- Key mapping
    ["keymap_showinventory"] = "Buka Inventaris",

    -- Salary related
    ["received_salary"] = "Anda telah dibayar: $%s",
    ["received_help"] = "Anda telah menerima dana kesejahteraan: $%s",
    ["company_nomoney"] = "perusahaan tempat Anda bekerja terlalu miskin untuk membayar gaji Anda",
    ["received_paycheck"] = "menerima gaji",
    ["bank"] = "Maze Bank",
    ["account_bank"] = "Bank",
    ["account_black_money"] = "Dirty Money",
    ["account_money"] = "Cash",

    ["act_imp"] = "Tidak dapat melakukan aksi",
    ["in_vehicle"] = "Tidak dapat melakukan aksi, Player di dalam kendaraan",
    ["not_in_vehicle"] = "Tidak dapat melakukan aksi, Player tidak di dalam kendaraan",

    -- Commands
    ["command_bring"] = "Bawa player kepadamu",
    ["command_car"] = "Munculkan kendaraan",
    ["command_car_car"] = "Model atau hash kendaraan",
    ["command_cardel"] = "Menghilangkan kendaraan di sekitar",
    ["command_cardel_radius"] = "Menghilangkan semua kendaraan dalam radius yang ditentukan",
    ["command_repair"] = "Perbaiki kendaraanmu",
    ["command_repair_success"] = "Berhasil memperbaiki kendaraan",
    ["command_repair_success_target"] = "Admin memperbaiki kendaraanmu",
    ["command_clear"] = "Bersihkan teks chat",
    ["command_clearall"] = "Bersihkan teks chat untuk semua player",
    ["command_clearinventory"] = "Hilangkan semua barang dari Inventaris Player",
    ["command_clearloadout"] = "Hilangkan semua senjata dari Loadout Player",
    ["command_freeze"] = "Bekukan player",
    ["command_unfreeze"] = "Kembalikan player",
    ["command_giveaccountmoney"] = "Beri uang ke rekening tertentu",
    ["command_giveaccountmoney_account"] = "Rekening untuk ditambahkan",
    ["command_giveaccountmoney_amount"] = "Jumlah untuk ditambahkan",
    ["command_giveaccountmoney_invalid"] = "Nama Rekening Salah",
    ["command_removeaccountmoney"] = "Hilangkan uang dari rekening tertentu",
    ["command_removeaccountmoney_account"] = "Rekening yang akan dihapus",
    ["command_removeaccountmoney_amount"] = "Jumlah yang dihapus",
    ["command_removeaccountmoney_invalid"] = "Nama Rekening Salah",
    ["command_giveitem"] = "Beri Player barang",
    ["command_giveitem_item"] = "Nama Barang",
    ["command_giveitem_count"] = "Jumlah",
    ["command_giveweapon"] = "Beri player senjata",
    ["command_giveweapon_weapon"] = "Nama Senjata",
    ["command_giveweapon_ammo"] = "Jumlah Amunisi",
    ["command_giveweapon_hasalready"] = "Player telah memiliki senjata ini",
    ["command_giveweaponcomponent"] = "Beri komponen senjata ke player",
    ["command_giveweaponcomponent_component"] = "Nama Komponen",
    ["command_giveweaponcomponent_invalid"] = "Komponen Senjata Salah",
    ["command_giveweaponcomponent_hasalready"] = "Player telah memiliki komponen senjata ini",
    ["command_giveweaponcomponent_missingweapon"] = "Player tidak memiliki senjata ini",
    ["command_goto"] = "Teleportasi dirimu ke player",
    ["command_kill"] = "Bunuh player",
    ["command_save"] = "Paksa Simpan Data player",
    ["command_saveall"] = "Paksa Simpan Data semua player",
    ["command_setaccountmoney"] = "Atur uang dalam rekening tertentu",
    ["command_setaccountmoney_amount"] = "Jumlah",
    ["command_setcoords"] = "Teleportasi ke koordinat tertentu",
    ["command_setcoords_x"] = "Posisi X",
    ["command_setcoords_y"] = "Posisi Y",
    ["command_setcoords_z"] = "Posisi Z",
    ["command_setjob"] = "Beri player pekerjaan",
    ["command_setjob_job"] = "Nama",
    ["command_setjob_grade"] = "Jabatan pekerjaan",
    ["command_setjob_invalid"] = "pekerjaan, jabatan atau keduanya salah",
    ["command_setgroup"] = "Atur izn grup player",
    ["command_setgroup_group"] = "Nama Grup",
    ["commanderror_argumentmismatch"] = "Jumlah Argument Salah (passed %s, wanted %s)",
    ["commanderror_argumentmismatch_number"] = "Argument #%s salah tipe data (passed string, wanted number)",
    ["commanderror_argumentmismatch_string"] = "Argument #%s salah tipe data (passed number, wanted string)",
    ["commanderror_invaliditem"] = "Barang salah",
    ["commanderror_invalidweapon"] = "Senjata salah",
    ["commanderror_console"] = "Perintah tidak dapat dieksekusi dari console",
    ["commanderror_invalidcommand"] = "Perintah Salah - /%s",
    ["commanderror_invalidplayerid"] = "Player tersebut tidak online",
    ["commandgeneric_playerid"] = "Id Server Player",
    ["command_giveammo_noweapon_found"] = "%s tidak memiliki senjata tersebut",
    ["command_giveammo_weapon"] = "Nama Senjata",
    ["command_giveammo_ammo"] = "Jumlah Amunisi",
    ["tpm_nowaypoint"] = "Titik Lokasi Tidak Diatur.",
    ["tpm_success"] = "Berhasil Teleportasi",

    ["noclip_message"] = "Noclip telah %s",
    ["enabled"] = "~g~diaktifkan~s~",
    ["disabled"] = "~r~dimatikan~s~",

    -- Locale settings
    ["locale_digit_grouping_symbol"] = ",",
    ["locale_currency"] = "£%s",

    -- Weapons

    -- Melee
    ["weapon_dagger"] = "Dagger",
    ["weapon_bat"] = "Bat",
    ["weapon_battleaxe"] = "Battle Axe",
    ["weapon_bottle"] = "Bottle",
    ["weapon_crowbar"] = "Crowbar",
    ["weapon_flashlight"] = "Flashlight",
    ["weapon_golfclub"] = "Golf Club",
    ["weapon_hammer"] = "Hammer",
    ["weapon_hatchet"] = "Hatchet",
    ["weapon_knife"] = "Knife",
    ["weapon_knuckle"] = "Knuckledusters",
    ["weapon_machete"] = "Machete",
    ["weapon_nightstick"] = "Nightstick",
    ["weapon_wrench"] = "Pipe Wrench",
    ["weapon_poolcue"] = "Pool Cue",
    ["weapon_stone_hatchet"] = "Stone Hatchet",
    ["weapon_switchblade"] = "Switchblade",

    -- Handguns
    ["weapon_appistol"] = "AP Pistol",
    ["weapon_ceramicpistol"] = "Ceramic Pistol",
    ["weapon_combatpistol"] = "Combat Pistol",
    ["weapon_doubleaction"] = "Double-Action Revolver",
    ["weapon_navyrevolver"] = "Navy Revolver",
    ["weapon_flaregun"] = "Flaregun",
    ["weapon_gadgetpistol"] = "Gadget Pistol",
    ["weapon_heavypistol"] = "Heavy Pistol",
    ["weapon_revolver"] = "Heavy Revolver",
    ["weapon_revolver_mk2"] = "Heavy Revolver MK2",
    ["weapon_marksmanpistol"] = "Marksman Pistol",
    ["weapon_pistol"] = "Pistol",
    ["weapon_pistol_mk2"] = "Pistol MK2",
    ["weapon_pistol50"] = "Pistol .50",
    ["weapon_snspistol"] = "SNS Pistol",
    ["weapon_snspistol_mk2"] = "SNS Pistol MK2",
    ["weapon_stungun"] = "Taser",
    ["weapon_raypistol"] = "Up-N-Atomizer",
    ["weapon_vintagepistol"] = "Vintage Pistol",

    -- Shotguns
    ["weapon_assaultshotgun"] = "Assault Shotgun",
    ["weapon_autoshotgun"] = "Auto Shotgun",
    ["weapon_bullpupshotgun"] = "Bullpup Shotgun",
    ["weapon_combatshotgun"] = "Combat Shotgun",
    ["weapon_dbshotgun"] = "Double Barrel Shotgun",
    ["weapon_heavyshotgun"] = "Heavy Shotgun",
    ["weapon_musket"] = "Musket",
    ["weapon_pumpshotgun"] = "Pump Shotgun",
    ["weapon_pumpshotgun_mk2"] = "Pump Shotgun MK2",
    ["weapon_sawnoffshotgun"] = "Sawed Off Shotgun",

    -- SMG & LMG
    ["weapon_assaultsmg"] = "Assault SMG",
    ["weapon_combatmg"] = "Combat MG",
    ["weapon_combatmg_mk2"] = "Combat MG MK2",
    ["weapon_combatpdw"] = "Combat PDW",
    ["weapon_gusenberg"] = "Gusenberg Sweeper",
    ["weapon_machinepistol"] = "Machine Pistol",
    ["weapon_mg"] = "MG",
    ["weapon_microsmg"] = "Micro SMG",
    ["weapon_minismg"] = "Mini SMG",
    ["weapon_smg"] = "SMG",
    ["weapon_smg_mk2"] = "SMG MK2",
    ["weapon_raycarbine"] = "Unholy Hellbringer",

    -- Rifles
    ["weapon_advancedrifle"] = "Advanced Rifle",
    ["weapon_assaultrifle"] = "Assault Rifle",
    ["weapon_assaultrifle_mk2"] = "Assault Rifle MK2",
    ["weapon_bullpuprifle"] = "Bullpup Rifle",
    ["weapon_bullpuprifle_mk2"] = "Bullpup Rifle MK2",
    ["weapon_carbinerifle"] = "Carbine Rifle",
    ["weapon_carbinerifle_mk2"] = "Carbine Rifle MK2",
    ["weapon_compactrifle"] = "Compact Rifle",
    ["weapon_militaryrifle"] = "Military Rifle",
    ["weapon_specialcarbine"] = "Special Carbine",
    ["weapon_specialcarbine_mk2"] = "Special Carbine MK2",
    ["weapon_heavyrifle"] = "Heavy Rifle",

    -- Sniper
    ["weapon_heavysniper"] = "Heavy Sniper",
    ["weapon_heavysniper_mk2"] = "Heavy Sniper MK2",
    ["weapon_marksmanrifle"] = "Marksman Rifle",
    ["weapon_marksmanrifle_mk2"] = "Marksman Rifle MK2",
    ["weapon_sniperrifle"] = "Sniper Rifle",

    -- Heavy / Launchers
    ["weapon_compactlauncher"] = "Compact Launcher",
    ["weapon_firework"] = "Firework Launcher",
    ["weapon_grenadelauncher"] = "Grenade Launcher",
    ["weapon_hominglauncher"] = "Homing Launcher",
    ["weapon_minigun"] = "Minigun",
    ["weapon_railgun"] = "Railgun",
    ["weapon_rpg"] = "Rocket Launcher",
    ["weapon_rayminigun"] = "Widowmaker",

    -- Criminal Enterprises DLC
    ["weapon_metaldetector"] = "Metal Detector",
    ["weapon_precisionrifle"] = "Precision Rifle",
    ["weapon_tactilerifle"] = "Service Carbine",

    -- Drug wars dlc
    ["weapon_candycane"] = "Candycane",
    ["weapon_acidpackage"] = "Acid Package",
    ["weapon_pistolxm3"] = "Pistol8 x3m",
    ["weapon_railgunxm3"] = "Railgun",

    -- Thrown
    ["weapon_ball"] = "Baseball",
    ["weapon_bzgas"] = "BZ Gas",
    ["weapon_flare"] = "Flare",
    ["weapon_grenade"] = "Grenade",
    ["weapon_petrolcan"] = "Jerrycan",
    ["weapon_hazardcan"] = "Hazardous Jerrycan",
    ["weapon_molotov"] = "Molotov Cocktail",
    ["weapon_proxmine"] = "Proximity Mine",
    ["weapon_pipebomb"] = "Pipe Bomb",
    ["weapon_snowball"] = "Snowball",
    ["weapon_stickybomb"] = "Sticky Bomb",
    ["weapon_smokegrenade"] = "Tear Gas",

    -- Special
    ["weapon_fireextinguisher"] = "Pemadam Api",
    ["weapon_digiscanner"] = "Digital Scanner",
    ["weapon_garbagebag"] = "Kantong Sampah",
    ["weapon_handcuffs"] = "Borgol",
    ["gadget_nightvision"] = "Night Vision",
    ["gadget_parachute"] = "Parasut",

    -- Weapon Components
    ["component_knuckle_base"] = "base Model",
    ["component_knuckle_pimp"] = "the Pimp",
    ["component_knuckle_ballas"] = "the Ballas",
    ["component_knuckle_dollar"] = "the Hustler",
    ["component_knuckle_diamond"] = "the Rock",
    ["component_knuckle_hate"] = "the Hater",
    ["component_knuckle_love"] = "the Lover",
    ["component_knuckle_player"] = "the Player",
    ["component_knuckle_king"] = "the King",
    ["component_knuckle_vagos"] = "the Vagos",

    ["component_luxary_finish"] = "luxary Weapon Finish",

    ["component_handle_default"] = "default Handle",
    ["component_handle_vip"] = "vIP Handle",
    ["component_handle_bodyguard"] = "bodyguard Handle",

    ["component_vip_finish"] = "vIP Finish",
    ["component_bodyguard_finish"] = "bodyguard Finish",

    ["component_camo_finish"] = "digital Camo",
    ["component_camo_finish2"] = "brushstroke Camo",
    ["component_camo_finish3"] = "woodland Camo",
    ["component_camo_finish4"] = "skull Camo",
    ["component_camo_finish5"] = "sessanta Nove Camo",
    ["component_camo_finish6"] = "perseus Camo",
    ["component_camo_finish7"] = "leopard Camo",
    ["component_camo_finish8"] = "zebra Camo",
    ["component_camo_finish9"] = "geometric Camo",
    ["component_camo_finish10"] = "boom Camo",
    ["component_camo_finish11"] = "patriotic Camo",

    ["component_camo_slide_finish"] = "digital Slide Camo",
    ["component_camo_slide_finish2"] = "brushstroke Slide Camo",
    ["component_camo_slide_finish3"] = "woodland Slide Camo",
    ["component_camo_slide_finish4"] = "skull Slide Camo",
    ["component_camo_slide_finish5"] = "sessanta Nove Slide Camo",
    ["component_camo_slide_finish6"] = "perseus Slide Camo",
    ["component_camo_slide_finish7"] = "leopard Slide Camo",
    ["component_camo_slide_finish8"] = "zebra Slide Camo",
    ["component_camo_slide_finish9"] = "geometric Slide Camo",
    ["component_camo_slide_finish10"] = "boom Slide Camo",
    ["component_camo_slide_finish11"] = "patriotic Slide Camo",

    ["component_clip_default"] = "default Magazine",
    ["component_clip_extended"] = "extended Magazine",
    ["component_clip_drum"] = "drum Magazine",
    ["component_clip_box"] = "box Magazine",

    ["component_scope_holo"] = "holographic Scope",
    ["component_scope_small"] = "small Scope",
    ["component_scope_medium"] = "medium Scope",
    ["component_scope_large"] = "large Scope",
    ["component_scope"] = "mounted Scope",
    ["component_scope_advanced"] = "advanced Scope",
    ["component_ironsights"] = "ironsights",

    ["component_suppressor"] = "suppressor",
    ["component_compensator"] = "compensator",

    ["component_muzzle_flat"] = "flat Muzzle Brake",
    ["component_muzzle_tactical"] = "tactical Muzzle Brake",
    ["component_muzzle_fat"] = "fat-End Muzzle Brake",
    ["component_muzzle_precision"] = "precision Muzzle Brake",
    ["component_muzzle_heavy"] = "heavy Duty Muzzle Brake",
    ["component_muzzle_slanted"] = "slanted Muzzle Brake",
    ["component_muzzle_split"] = "split-End Muzzle Brake",
    ["component_muzzle_squared"] = "squared Muzzle Brake",

    ["component_flashlight"] = "flashlight",
    ["component_grip"] = "grip",

    ["component_barrel_default"] = "default Barrel",
    ["component_barrel_heavy"] = "heavy Barrel",

    ["component_ammo_tracer"] = "tracer Ammo",
    ["component_ammo_incendiary"] = "incendiary Ammo",
    ["component_ammo_hollowpoint"] = "hollowpoint Ammo",
    ["component_ammo_fmj"] = "fMJ Ammo",
    ["component_ammo_armor"] = "armor Piercing Ammo",
    ["component_ammo_explosive"] = "armor Piercing Incendiary Ammo",

    ["component_shells_default"] = "default Shells",
    ["component_shells_incendiary"] = "dragons Breath Shells",
    ["component_shells_armor"] = "steel Buckshot Shells",
    ["component_shells_hollowpoint"] = "flechette Shells",
    ["component_shells_explosive"] = "explosive Slug Shells",

    -- Weapon Ammo
    ["ammo_rounds"] = "peluru",
    ["ammo_shells"] = "peluru",
    ["ammo_charge"] = "peluru",
    ["ammo_petrol"] = "gallon bahan bakar",
    ["ammo_firework"] = "kembang api",
    ["ammo_rockets"] = "roket",
    ["ammo_grenadelauncher"] = "granat",
    ["ammo_grenade"] = "granat",
    ["ammo_stickybomb"] = "bom",
    ["ammo_pipebomb"] = "bom pipa",
    ["ammo_smokebomb"] = "bom asap",
    ["ammo_molotov"] = "molotov",
    ["ammo_proxmine"] = "peledak",
    ["ammo_bzgas"] = "kaleng",
    ["ammo_ball"] = "bola",
    ["ammo_snowball"] = "bola salju",
    ["ammo_flare"] = "flare",
    ["ammo_flaregun"] = "flare",

    -- Weapon Tints
    ["tint_default"] = "default skin",
    ["tint_green"] = "green skin",
    ["tint_gold"] = "gold skin",
    ["tint_pink"] = "pink skin",
    ["tint_army"] = "army skin",
    ["tint_lspd"] = "blue skin",
    ["tint_orange"] = "orange skin",
    ["tint_platinum"] = "platinum skin",
    -- MK2 Weapon Tints
    ["tint_classic_black"] = "hitam klasik",
    ["tint_classic_gray"] = "abu-abu klasik",
    ["tint_classic_two_tone"] = "dua warna klasik",
    ["tint_classic_white"] = "putih klasik",
    ["tint_classic_beige"] = "beige klasik",
    ["tint_classic_green"] = "hijau klasik",
    ["tint_classic_blue"] = "biru klasik",
    ["tint_classic_earth"] = "tanah klasik",
    ["tint_classic_brown_black"] = "coklat hitam klasik",
    ["tint_contrast_red"] = "merah kontras",
    ["tint_contrast_blue"] = "biru kontras",
    ["tint_contrast_yellow"] = "kuning kontras",
    ["tint_contrast_orange"] = "oren kontras",
    ["tint_bold_pink"] = "pink berani",
    ["tint_bold_purple_yellow"] = "ungu kuning berani",
    ["tint_bold_orange"] = "oren berani",
    ["tint_bold_green_purple"] = "hijau ungu berani",
    ["tint_bold_red_feat"] = "merah berani feat",
    ["tint_bold_green_feat"] = "hijau berani feat",
    ["tint_bold_cyan_feat"] = "sian berani feat",
    ["tint_bold_yellow_feat"] = "kuning berani feat",
    ["tint_bold_red_white"] = "merah putih berani",
    ["tint_bold_blue_white"] = "biru putih berani",
    ["tint_metallic_gold"] = "emas metalik",
    ["tint_metallic_platinum"] = "platina metalik",
    ["tint_metallic_gray_lilac"] = "abu-abu lilac metalik",
    ["tint_metallic_purple_lime"] = "ungu jeruk nipis metalik",
    ["tint_metallic_red"] = "merah metalik",
    ["tint_metallic_green"] = "hijau metalik",
    ["tint_metallic_blue"] = "biru metalik",
    ["tint_metallic_white_aqua"] = "putih aqua metalik",
    ["tint_metallic_red_yellow"] = "merah kuning metalik",
}
